html,
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  overflow-x: hidden;
  font-family: Helvetica, Arial, sans-serif;
  font-weight: 700;
  color: rgb(0, 0, 0);

  background: #ffffff;
}

.app-container {
  background-color: #f0f7ff;
  min-height: calc(100vh - 70px);
  padding: 20px;
  border-radius: 35px 35px 0 0;
}

// PrimeNG Dialog Customizations
.p-dialog {
  .p-dialog-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
  }

  .p-dialog-content {
    padding: 0;
  }

  .p-dialog-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid #e9ecef;
  }
}

// Event Reject Dialog Specific Styles
.event-reject-dialog {
  .p-dropdown-panel {
    width: 100%;
  }

  .p-dropdown-items-wrapper {
    max-height: 200px;
  }
}

// Global Pagination Styles
// For Bootstrap pagination
.pagination {
  .page-link {
    color: #dc3545;
    border-color: #dee2e6;
    padding: 0.375rem 0.75rem;

    &:hover {
      background-color: #f8f9fa;
      border-color: #dee2e6;
      color: #dc3545;
    }
  }

  .page-item.active .page-link {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
  }
}

// For PrimeNG pagination
:root {
  --pagination-color: #dc3545;
}

// Global PrimeNG paginator styling
:host ::ng-deep .p-paginator,
::ng-deep .p-paginator {
  .p-paginator-page.p-highlight {
    background-color: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
  }

  .p-paginator-page:not(.p-highlight):hover,
  .p-paginator-next:hover,
  .p-paginator-prev:hover,
  .p-paginator-first:hover,
  .p-paginator-last:hover {
    color: #dc3545 !important;
  }

  .p-paginator-page,
  .p-paginator-next,
  .p-paginator-prev,
  .p-paginator-first,
  .p-paginator-last {
    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
  }
}

// Global Back Button Styles
.back-button {
  display: flex;
  align-items: center;
  color: #0d6efd !important; // Blue color for all back buttons
  text-decoration: none;
  font-weight: 500;
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;

  i,
  .pi,
  .bi {
    margin-right: 0.5rem;
    font-size: 1.2rem;
    color: #0d6efd !important; // Blue color for icons
  }

  span {
    font-size: 1rem;
    color: #212529; // Dark color for text
  }

  &:hover {
    text-decoration: none;

    i,
    .pi,
    .bi {
      color: #0a58ca !important; // Darker blue on hover
    }
  }
}
